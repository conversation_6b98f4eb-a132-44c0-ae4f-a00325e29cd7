import axios from 'axios';
import Router from 'next/router';

// Prevent multiple redirects on 401
let isRedirecting = false;

// Create axios instance
const api = axios.create({
  withCredentials: true,
});

// Add a response interceptor
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response && error.response.status === 401) {
        console.log('401 detected by interceptor');
        if (typeof window !== 'undefined' && !isRedirecting) {
          if (window.location.pathname !== '/auth/login') {
            isRedirecting = true;
            localStorage.removeItem('persist:root');
            sessionStorage.clear();
            (window as any).store?.dispatch({ type: 'auth/clearAuthState' });
            Router.replace('/auth/login');
          }
        }
      }
    return Promise.reject(error);
  }
);

export default api;
